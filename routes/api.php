<?php

use App\Models\User;
use App\Models\Fleet\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\ReportAllController;
use App\Http\Controllers\Fleet\StockController;
use App\Http\Controllers\Fleet\OpnameController;
use App\Http\Controllers\Driver\DriverController;
use App\Http\Controllers\Fleet\InboundController;
use App\Http\Controllers\Fleet\MekanikController;
use App\Http\Controllers\Fleet\RackingController;
use App\Http\Controllers\Fleet\SOAssetController;
use App\Http\Controllers\Fleet\OutboundController;
use App\Http\Controllers\Fleet\BASOAssetController;
use App\Http\Controllers\Fleet\DashboardController;
use App\Http\Controllers\Fleet\LentAssetController;
use App\Http\Controllers\Fleet\ListAssetController;
use App\Http\Controllers\Fleet\MappingSOController;
use App\Http\Controllers\Fleet\MappingNonController;
use App\Http\Controllers\FleetMaster\MerkController;
use App\Http\Controllers\Fleet\BeritaAcaraController;
use App\Http\Controllers\Fleet\MutasiAssetController;
use App\Http\Controllers\FleetMaster\ColorController;
use App\Http\Controllers\FleetMaster\EngineController;
use App\Http\Controllers\Workshop\InsuranceController;
use App\Http\Controllers\Workshop\PengajuanController;
use App\Http\Controllers\Fleet\DisposalAssetController;
use App\Http\Controllers\Fleet\HandOverAssetController;
use App\Http\Controllers\FleetMaster\VehicleController;
use App\Http\Controllers\Technical\TechnicalController;
use App\Http\Controllers\Fleet\IssuedAssetCodeController;
use App\Http\Controllers\Fleet\LapHasilBAAssetController;
use App\Http\Controllers\Fleet\WaitingAssetCodeController;
use App\Http\Controllers\Fleet\InboundOutboundRecapController;
use App\Http\Controllers\FleetMaster\CategoryVehicleController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::get('/generate-username', function () {
//    $users = User::all();
//    foreach ($users as $key => $user) {
//       $update = User::find($user->id);
//       $email = explode('@',$update->email);
//       $update->username = $email[0];
//       $update->save();
//    }
// });

Route::get('user-get-object', 'GPS\FleetController@userGetObject');

Route::get("form-perawatan-kendaraan",[PengajuanController::class,'perawatanKendaraan']);
// Route::post('login', 'Api\AuthController@login');
Route::post('register', 'Api\RegisterController@register');
Route::post('forgot', 'Api\ForgotController@forgot');
Route::post('reset', 'Api\ForgotController@reset');
Route::get('email/resend/{user}', 'Api\VerifyController@resend')->name('verification.resend');
Route::get('email/verify/{id}', 'Api\VerifyController@verify')->name('verification.verify');

Route::get('user-ljr', 'Fleet\UserLjrController@index');
Route::get('user-ljr/employee', 'Fleet\UserLjrController@employee');
Route::get('user-ljr/syncrone', function(){
    Artisan::call('synchrone:user');
});
Route::post('login', 'Fleet\UserLjrController@login');

Route::group(['middleware' => ['auth:api']], function () {
    Route::post('logout', 'Api\AuthController@logout');
    Route::get('user-ljr/set-role', 'Fleet\UserLjrController@setRole');
    // Route::post('check-user-hrm', 'Fleet\UserLjrController@checkUserHrm');
    Route::get('user', 'Api\AuthController@user');
    Route::post('history-location', 'Api\AuthController@historyLocation');
    Route::resource('divisi', 'Fleet\DivisiController');

    Route::get('list-user', 'Fleet\UserController@index');
    Route::post('user/update-email', 'Fleet\UserController@updateEmail');
    Route::get('user/create', 'Fleet\UserController@create');
    Route::get('edit/user/{id}', 'Fleet\UserController@edit');
    Route::post('create/user', 'Fleet\UserController@store');
    Route::post('update/user/{id}', 'Fleet\UserController@update');
    Route::post('user/delete/{id}', 'Fleet\UserController@delete');
    Route::post('user/forgot-password', 'Fleet\UserController@forgotPassword');
    Route::post('user/change-profile-image', 'Fleet\UserController@changeProfileImage');
    Route::put('user/photo-profile', 'Fleet\UserController@photoProfile');
    Route::resource('department', 'Fleet\DepartmentController');
    Route::resource('section', 'Fleet\SectionController');
    Route::resource('sub-section', 'Fleet\SubSectionController');
    Route::resource('currencies', 'Fleet\CurrencyController');
    Route::resource('company', 'Fleet\CompanyController');
    Route::resource('supplier-category', 'Fleet\SupplierCategoryController');
    Route::resource('supplier-type', 'Fleet\SupplierTypeController');
    Route::resource('supplier', 'Fleet\SupplierController');
    Route::resource('shipper', 'Fleet\ShipperController');
    Route::resource('unit-of-measure', 'Fleet\UnitOfMeasureController');
    Route::resource('stock-master-group', 'Fleet\StockMasterGroupController');
    Route::resource('stock-master-class', 'Fleet\StockMasterClassController');
    Route::resource('role', 'Fleet\RoleController');
    Route::resource('system-setting', 'Fleet\SystemSettingController');
    Route::resource('payment-term', 'Fleet\PaymentTermController');
    Route::resource('payment-methods', 'Fleet\PaymentMethodsController');
    Route::resource('location-maintenance', 'Fleet\LocationMaintenanceController');
    Route::resource('stock-category', 'Fleet\StockCategoryController');
    Route::resource('discount-category', 'Fleet\DiscountCategoryController');
    Route::resource('branch', 'Fleet\BranchController');
    Route::resource('item-maintenance', 'Fleet\ItemMaintenanceController');
    Route::get('item-maintenance-by-type', 'Fleet\ItemMaintenanceController@getItemByType');
    Route::resource('vendor', 'Fleet\VendorController');
    Route::resource('vendor-category', 'Fleet\VendorCategoryController');
    Route::resource('vendor-type', 'Fleet\VendorTypeController');
    Route::resource('tax-categories', 'Fleet\TaxCategoryController');
    Route::resource('tax-group', 'Fleet\TaxGroupController');
    Route::resource('tax-authority', 'Fleet\TaxAuthorityController');
    Route::resource('driverfleet', 'Fleet\DriverController');
    Route::post('driverfleet/singkronisasi', 'Fleet\DriverController@sync');
    Route::post('driverfleet/get-location', 'Fleet\DriverController@getLocation');
    Route::get('nation', 'Fleet\LocationMaintenanceController@nation');
    Route::get('tax-category', 'Fleet\StockCategoryController@taxCategory');
    Route::get('stock-type', 'Fleet\StockCategoryController@stockType');
    Route::get('chart-master-pandl-0', 'Fleet\StockCategoryController@chartMasterPandl0');
    Route::get('chart-master-pandl-1', 'Fleet\StockCategoryController@chartMasterPandl1');
    Route::get('group-barang', 'Fleet\ItemMaintenanceController@groupBarang');
    Route::get('class-barang', 'Fleet\ItemMaintenanceController@classBarang');
    Route::get('stock-category-GNRIVT', 'Fleet\ItemMaintenanceController@stockCategoryGNRIVT');

    Route::prefix('vehicle')->group(function() {
        Route::get("merk",[MerkController::class,'index']);
        Route::get("merk-edit/{id}",[MerkController::class,'edit']);
        Route::post("merk-store/{any}",[MerkController::class,'store']);
        Route::get("merk-delete/{id}",[MerkController::class,'delete']);

        Route::get("color",[ColorController::class,'index']);
        Route::get("color-edit/{id}",[ColorController::class,'edit']);
        Route::post("color-store/{any}",[ColorController::class,'store']);
        Route::get("color-delete/{id}",[ColorController::class,'delete']);

        Route::get("engine",[EngineController::class,'index']);
        Route::get("engine-edit/{id}",[EngineController::class,'edit']);
        Route::post("engine-store/{any}",[EngineController::class,'store']);
        Route::get("engine-delete/{id}",[EngineController::class,'delete']);

        Route::get("category",[CategoryVehicleController::class,'index']);
        Route::get("category-edit/{id}",[CategoryVehicleController::class,'edit']);
        Route::put("category-store/{any}",[CategoryVehicleController::class,'store']);
        Route::get("category-delete/{id}",[CategoryVehicleController::class,'delete']);
    });

    Route::prefix('kendaraan')->group(function() {
        Route::get("",[VehicleController::class,'index']);
        Route::get("create",[VehicleController::class,'create']);
        Route::get("edit/{id}",[VehicleController::class,'edit']);
        Route::get("drivers/{id}",[VehicleController::class,'drivers']);
        Route::get("delete/{id}",[VehicleController::class,'delete']);
        Route::get("delete-driver/{id}",[VehicleController::class,'removeDriver']);
        Route::post("update/{id}",[VehicleController::class,'update']);
        Route::post("store",[VehicleController::class,'store']);
        Route::post("store_driver/{any}",[VehicleController::class,'store_driver']);
        Route::get("km-actual/{id}",[VehicleController::class,'kmActual']);
        Route::post("km-actual/{id}",[VehicleController::class,'kmActualStore']);
        Route::delete("km-actual/{id}",[VehicleController::class,'kmActualDelete']);
        Route::get("kir/{id}",[VehicleController::class,'kir']);
        Route::post("kir/{id}",[VehicleController::class,'kirStore']);
        Route::delete("kir/{id}",[VehicleController::class,'kirDelete']);
        // Route::get("service/{id}",[VehicleController::class,'service']);
        // Route::post("service/{id}",[VehicleController::class,'serviceStore']);
        Route::delete("service/{id}",[VehicleController::class,'serviceDelete']);
        Route::put("emergency",[VehicleController::class,'emergency']);
        Route::put("service",[VehicleController::class,'service']);
        Route::put("accident/{id}",[VehicleController::class,'accident']);
    });

    Route::prefix('driver')->group(function() {
        Route::get("/",[DriverController::class,'index']);
        Route::get("/create",[DriverController::class,'create']);
        Route::get("component",[DriverController::class,'component']);
        Route::put("store",[DriverController::class,'store']);
        Route::get("edit/{id}",[DriverController::class,'edit']);
        Route::put("update/{id}",[DriverController::class,'update']);
        Route::get("delete/{id}",[DriverController::class,'delete']);
    });

    Route::prefix('technical')->group(function() {
        Route::get("/",[TechnicalController::class,'index']);
        Route::put("store",[TechnicalController::class,'store']);
        Route::get("edit/{id}",[TechnicalController::class,'edit']);
        Route::put("update/{id}",[TechnicalController::class,'update']);
        Route::get("delete/{id}",[TechnicalController::class,'delete']);
    });

    Route::resource("insurance", 'Workshop\InsuranceController');

    Route::group(['prefix' => 'stock-non-aset'], function() {
        Route::group(['prefix' => 'inbound'], function() {
            Route::get('/', [InboundController::class, 'index']);
            Route::get('/check-inbound/{id}', [InboundController::class, 'checkInbound']);
            Route::get('/list', [InboundController::class, 'list']);
            Route::get('/choose-item/{no_inbound}', [InboundController::class, 'chooseItem']);
            Route::post('/choose-item/{no_inbound}', [InboundController::class, 'prosesChooseItem']);
            Route::get('/detail/{id}', [InboundController::class, 'detail']);
            Route::post('/{id}', [InboundController::class, 'prosesInbound']);
            Route::get('/no-inbound', [InboundController::class, 'NoInbound']);
        });
        Route::group(['prefix' => 'outbound'], function() {
            Route::get('/', [OutboundController::class, 'index']);
            Route::get('/list', [OutboundController::class, 'list']);
            Route::get('/choose-item/{no_rf}', [OutboundController::class, 'chooseItem']);
            Route::post('/choose-item/{no_rf}', [OutboundController::class, 'prosesChooseItem']);
            Route::get('/detail/{id}', [OutboundController::class, 'detail']);
            Route::post('/{id}', [OutboundController::class, 'prosesOutbound']);
            Route::post('/{id}', [OutboundController::class, 'prosesOutbound']);
            Route::get('/received-non-asset/{no_rf}', [OutboundController::class, 'viewReceivedNonAsset']);
        });
        Route::group(['prefix' => 'stock'], function() {
            Route::get('/', [StockController::class, 'index']);
            Route::get('/id-items', [StockController::class, 'IdItem']);
            Route::get('/loc-racking', [StockController::class, 'LocRacking']);
            Route::get('/detail/{id}', [StockController::class, 'detail']);
        });
        Route::group(['prefix' => 'inbound-outbound-recap'], function() {
            Route::get('/', [InboundOutboundRecapController::class, 'index']);
        });
    });

    Route::group(['prefix' => 'asset'], function() {
        Route::group(['prefix' => 'waiting-asset-code'], function() {
            Route::get('/', [WaitingAssetCodeController::class, 'index']);
            Route::get('/{id}/input-data', [WaitingAssetCodeController::class, 'inputData']);
            Route::post('/{id}', [WaitingAssetCodeController::class, 'store']);
            Route::get('no_pr', [WaitingAssetCodeController::class, 'no_pr']);
            Route::get('no_gr', [WaitingAssetCodeController::class, 'no_gr']);
            Route::get('no_cam', [WaitingAssetCodeController::class, 'no_cam']);
            Route::get('no_rf', [WaitingAssetCodeController::class, 'no_rf']);
        });
        Route::group(['prefix' => 'issued-asset-code'], function() {
            Route::get('/', [IssuedAssetCodeController::class, 'index']);
            Route::get('/{code}', [IssuedAssetCodeController::class, 'detail']);
            Route::post('/{code}', [IssuedAssetCodeController::class, 'store']);

        });
        Route::group(['prefix' => 'list-asset'], function() {
            Route::get('/', [ListAssetController::class, 'index']);
            Route::get('/asuransi-kendaraan', [ListAssetController::class, 'asuransiKendaraan']);
            Route::post('/asuransi-kendaraan/store', [ListAssetController::class, 'asuransiKendaraanStore']);
            Route::get('/create', [ListAssetController::class, 'create']);
            Route::post('/upload-media', [ListAssetController::class, 'uploadMedia']);
        });
        Route::post('/create/{name}', [ListAssetController::class, 'store']);
        Route::get('/getAsset/{id}', [ListAssetController::class, 'getAsset']);
        Route::get('/process-asset/{id}', [ListAssetController::class, 'processAsset']);

        Route::post('/handover/{id}', [HandOverAssetController::class, 'store']);
        Route::get('/handover/list-hand-over', [HandOverAssetController::class, 'listHandover']);
        Route::post('/list-hand-over/{id}', [HandOverAssetController::class, 'processListHandover']);
        Route::post('/mutasi/{id}', [MutasiAssetController::class, 'store']);
        Route::post('/lent/{id}', [LentAssetController::class, 'store']);
        Route::post('/disposal/{id}', [DisposalAssetController::class, 'store']);
        Route::get('/disposal/list-disposal', [DisposalAssetController::class, 'listDisposal']);
        Route::post('/list-disposal/{id}', [DisposalAssetController::class, 'processListDisposal']);
        Route::post('/lent/{id}', [LentAssetController::class, 'store']);
        Route::get('/lent/list-lent', [LentAssetController::class, 'listLent']);
        Route::post('/list-lent/{id}', [LentAssetController::class, 'processListLent']);
        Route::post('/mutasi/{id}', [MutasiAssetController::class, 'store']);
        Route::get('/mutasi/list-mutasi', [MutasiAssetController::class, 'listMutasi']);
        Route::post('/list-mutasi/{id}', [MutasiAssetController::class, 'processListMutasi']);
    });

    Route::prefix('workshop')->group(function() {
        // Route::get("insurance-edit/{id}",[InsuranceController::class,'edit']);
        // Route::put("insurance-store",[InsuranceController::class,'store']);
        // Route::get("insurance-delete/{id}",[InsuranceController::class,'delete']);
        Route::post("insurance-import",[InsuranceController::class,'import']);

        Route::get("tingkat-kerusakan",[PengajuanController::class,'tingkatKerusakan']);

        Route::get("dashboard/ga-ho",[PengajuanController::class,'dashboardGahoAll']);
        Route::get("ga-ho",[PengajuanController::class,'indexGahoAll']);
        Route::get("report",[PengajuanController::class,'report']);

        Route::get("keputusan-ga-ho",[PengajuanController::class,'indexGaho']);
        Route::get("keputusan-ga-ho/{id}",[PengajuanController::class,'detailGaho']);
        Route::get("detail-pemeriksaan-pengerjaan/{id}",[PengajuanController::class,'detailPemeriksaanPengerjaan']);
        Route::get("detail-pengajuan/{id}",[PengajuanController::class,'detailPengajuan']);
        Route::post("keputusan-ga-ho",[PengajuanController::class,'keputusanGaho']);

        Route::resource('mekanik', 'Fleet\MekanikController');
        Route::get('mekanik-user', [MekanikController::class, 'user']);
        Route::post('mekanik/store-item/{id}', [MekanikController::class, 'storeItem']);

        Route::get("sla",[PengajuanController::class,'indexSla']);
        Route::post("penetapan-sla/{id}",[PengajuanController::class,'penetapanSla']);
        // Route::get("work-estimate",[PengajuanController::class,'workEstimate']);
        Route::resource("work-estimate", 'Fleet\WorkEstimateController');
        Route::get("searchHistoryBengkel",[PengajuanController::class,'searchHistoryBengkel']);
        Route::post("reset-kendaraan/{id}",[PengajuanController::class,'resetKendaraan']);

        Route::get("pemeriksaan-pengerjaan",[PengajuanController::class,'indexPemeriksaanPengerjaan']);
        Route::post("pemeriksaan-pengerjaan/{id}",[PengajuanController::class,'pemeriksaanPengerjaan']);
        Route::post("submit-pemeriksaan-pengerjaan/{id}",[PengajuanController::class,'submitPemeriksaanPengerjaan']);
        Route::post("store-pemeriksaan-pengerjaan/{id}",[PengajuanController::class,'storePemeriksaan']);

        Route::get("keputusan-akhir-gaho",[PengajuanController::class,'indexKeputusanFinalGaho']);
        Route::post("keputusan-akhir-gaho/{id}",[PengajuanController::class,'keputusanFinalGaho']);

        Route::get("keputusan-akhir-kordi",[PengajuanController::class,'indexKeputusanFinalKoordniator']);
        Route::post("keputusan-akhir-kordi/{id}",[PengajuanController::class,'keputusanFinalKoordniator']);

        Route::prefix('pengajuan')->group(function() {
            Route::get("/",[PengajuanController::class,'index']);
            Route::get("create",[PengajuanController::class,'create']);
            Route::get("edit/{id}",[PengajuanController::class,'edit']);
            Route::get("show/{id}",[PengajuanController::class,'show']);
            Route::get("driver/{id}",[PengajuanController::class,'getDriver']);
            Route::get("element/{id}",[PengajuanController::class,'elements']);
            Route::put("store/{any}",[PengajuanController::class,'store']);
            Route::post("rejected",[PengajuanController::class,'rejected']);
            Route::post("approval",[PengajuanController::class,'approval_store']);
            Route::get("delete/{id}",[PengajuanController::class,'delete']);
        });
    });
    Route::resource('service-type', 'Workshop\ServiceTypeController');

    Route::resource('pr-approval-setting', 'GA\PRAppSetController');
    Route::resource('cq-approval-setting', 'GA\CQSettingController');
    Route::resource('po-approval-setting', 'GA\POSettingController');
    Route::resource('wo-approval-setting', 'GA\WOSettingController');
    Route::resource('destruction-approval-setting', 'GA\DestructionSettingController');
    Route::resource('purchase-request', 'GA\PurchaseRequestController');
    Route::get('purchase-request/{id}/pickup', 'GA\PurchaseRequestController@pickup');
    Route::post('purchase-request/{id}/pickup', 'GA\PurchaseRequestController@pickupStore');
    Route::resource('pr-approval', 'GA\PRApprovalSettingController');
    Route::resource('question', 'Fleet\QuestionController');
    Route::get('answer', 'Fleet\QuestionController@answerIndex');
    Route::post('answer', 'Fleet\QuestionController@answerStore');
    Route::post('cek-answer/{id}', 'Fleet\ChecklistVehicleController@cekAnswer');
    Route::resource('category-question', 'Fleet\CategoryQuestionController');
    Route::resource('status-vehicle', 'Fleet\StatusVehicleController');
    Route::post('vehicle-emergency', 'Fleet\ChecklistVehicleController@emergency');
    Route::post('checklist/upload-image', 'Fleet\ChecklistVehicleController@uploadImageChecklist');
    Route::get('checklist-vehicle/index-k3', 'Fleet\ChecklistVehicleController@indexK3');
    Route::get('checklist-vehicle/search-history-checklist', 'Fleet\ChecklistVehicleController@searchHistoryChecklist');
    Route::post('checklist-vehicle/search-history-checklist/{id}', 'Fleet\ChecklistVehicleController@resetHistoryChecklist');
    Route::get('checklist-vehicle/group-by-color', 'Fleet\ChecklistVehicleController@groupByColor');
    Route::get('checklist-vehicle/group-by-category/{id}', 'Fleet\ChecklistVehicleController@groupByCategory');
    Route::post('checklist-vehicle/approvedQuestion', 'Fleet\ChecklistVehicleController@approvedQuestion');
    Route::get('checklist-vehicle/cheklistStatus/{id}', 'Fleet\ChecklistVehicleController@cheklistStatus');
    Route::post('checklist-vehicle/createQuestionUser', 'Fleet\ChecklistVehicleController@createQuestionUser');
    Route::post('checklist-vehicle/create-question-vehicle-user', 'Fleet\ChecklistVehicleController@createQuestionVehicleUser');
    Route::post('checklist-vehicle/storeByCategory', 'Fleet\ChecklistVehicleController@storeByCategory');
    Route::post('checklist-vehicle/store-by-category-vehicle', 'Fleet\ChecklistVehicleController@storeByCategoryVehicle');
    Route::post('checklist-vehicle/finishStoreByCategory', 'Fleet\ChecklistVehicleController@finishStoreByCategory');
    Route::post('checklist-vehicle/inputNopol', 'Fleet\ChecklistVehicleController@inputNopol');
    Route::get('checklist-vehicle/masuk-bengkel/{id}', 'Fleet\ChecklistVehicleController@masukBengkel');
    Route::get('checklist-vehicle/tetap-lanjut/{id}', 'Fleet\ChecklistVehicleController@tetapLanjut');
    Route::post('checklist-vehicle/scan-vehicle-security', 'Fleet\ChecklistVehicleController@scanVehicleSecurity');
    Route::get('checklist-vehicle/scan-vehicle-security/list', 'Fleet\ChecklistVehicleController@searchHistoryChecklistSecurity');
    Route::post('checklist-vehicle/scan-vehicle-finance', 'Fleet\ChecklistVehicleController@scanVehicleFinance');
    Route::resource('checklist-vehicle', 'Fleet\ChecklistVehicleController');
    Route::get('detail-checklist-kendaraan/{id}', 'Fleet\ChecklistVehicleController@detailCheck');
    Route::post('checklist-health-created', 'Fleet\ChecklistHealthController@checklistHealthCreate');
    Route::resource('checklist-health', 'Fleet\ChecklistHealthController');
    Route::get('detail-checklist-kesehatan/{id}', 'Fleet\ChecklistHealthController@detailCheck');
    Route::resource('quiz', 'Fleet\QuizController');
    Route::resource('k3', 'Fleet\K3Controller');
    Route::post('cek-status', 'Fleet\K3Controller@cekKeputusan');
    Route::post('categoryReportVehicle', 'Fleet\K3Controller@categoriesReportVehicle');
    Route::post('terima-kendaraan/{id}', 'Fleet\K3Controller@approveKendaraanMerah');
    Route::post('koordinator', 'Fleet\K3Controller@koordinator');
    Route::post('koordinator/{id}', 'Fleet\K3Controller@detailKordinator');
    Route::post('riwayat-koordinator', 'Fleet\K3Controller@riwayatKoordinator');
    Route::post('riwayat-answer-questionUser', 'Fleet\K3Controller@indexRiwayatAnswerQuestionUser');
    Route::post('k3-list-check', 'Fleet\K3Controller@listDashboard');
    Route::get('k3-detail/{id}', 'Fleet\K3Controller@detailDashboard');
    Route::post('k3/pulangkan/{id}', 'Fleet\K3Controller@pulangkan');
    Route::get('k3/get-hasil/{id}', 'Fleet\K3Controller@getHasil');
    Route::post('k3/waiting/{id}', 'Fleet\K3Controller@waiting');
    Route::post('k3/tindakan', 'Fleet\K3Controller@tindakan');
    Route::post('k3/action-k3/{id}', 'Fleet\K3Controller@actionK3');
    Route::get('report-kendaraan', 'Fleet\K3Controller@indexCheklistKendaraan');
    Route::get('report-k3', 'Fleet\K3Controller@indexCheklistKesehatan');
    Route::resource('type-sim', 'Fleet\TypeSimController');
    Route::resource('pr-compare', 'GA\PRCompareController');
    Route::get('pr-compare/submit/{id}', 'GA\PRCompareController@submitNego');
    Route::resource('cq-approval', 'GA\CompareQAController');

    //Purchace Order
    Route::get('purchase-order/create-list', 'GA\PurchaseOrderController@getListRfDetail');
    // Route::post('purchase-order/create-po-list', 'GA\PurchaseOrderController@sessionItemList');
    Route::get('list_nopol', [ListAssetController::class, 'list_nopol']);
    Route::get('cek_nopol', [ListAssetController::class, 'cek_nopol']);
    Route::post('reset_nopol', [ListAssetController::class, 'reset']);
    Route::get('no_asset', [IssuedAssetCodeController::class, 'no_asset']);
    Route::get('code_aset', [ListAssetController::class, 'code_aset']);
    Route::get('list-asuransi', [ListAssetController::class, 'no_list_asuransi']);
    Route::get('code_aset_handover', [HandOverAssetController::class, 'code_aset_handover']);
    Route::get('code_aset_mutasi', [MutasiAssetController::class, 'code_aset_mutasi']);
    Route::get('code_aset_lent', [LentAssetController::class, 'code_aset_lent']);
    Route::get('code_aset_disposal', [DisposalAssetController::class, 'code_aset_disposal']);
    Route::get('no_pr', 'GA\PurchaseOrderController@no_pr');
    Route::get('gudang', 'Fleet\RackingController@racking');
    Route::get('lokasi_gudang', 'Fleet\RackingController@lokasi_gudang');
    Route::get('kode_lokasi_gudang', 'Fleet\RackingController@kode_lokasi_gudang');
    Route::get('no_gudang', 'Fleet\RackingController@no_gudang');
    Route::get('no_raw', 'Fleet\RackingController@no_raw');
    Route::get('no_racking', 'Fleet\RackingController@no_racking');
    Route::get('no_bin', 'Fleet\RackingController@no_bin');
    Route::get('no_po', 'GA\PurchaseOrderController@no_po');
    Route::get('no_cam', 'GA\PurchaseOrderController@no_cam');
    Route::get('no_handover', 'Fleet\HandOverController@no_handover');
    Route::get('no_rf', 'Fleet\HandOverController@no_rf');
    Route::get('no_good', 'GA\GoodReceivedController@no_good');
    Route::get('codes', 'Fleet\MasterTypeAssetController@codes');
    Route::get('cat_code', 'Fleet\CategoryItemController@codes');
    Route::get('satuan', 'Fleet\TypeItemController@satuan');
    Route::get('id-items', 'Fleet\TypeItemController@id_items');
    Route::post('purchase-order/create-po', 'GA\PurchaseOrderController@createdPO');
    Route::get('purchase-order/create-po-list', 'GA\PurchaseOrderController@getSessionItemList');
    Route::get('list-supplier', 'GA\PurchaseOrderController@listSupplier');
    Route::post('purchase-order/approval-po/{id}', 'GA\PurchaseOrderController@approvePo');
    Route::post('purchase-order/approval-pr/{id}', 'GA\PurchaseOrderController@approvePr');
    Route::resource('purchase-order', 'GA\PurchaseOrderController');
    Route::resource('purchase-order-detail', 'GA\PurchaseOrderDetailController');
    Route::get('purchase-order/create-po/{id}/{supplier}', 'GA\PurchaseOrderController@createPo');
    Route::post('purchase-order/store/{id}/{supplier}', 'GA\PurchaseOrderController@store');
    Route::get('purchase-order/show-po/{id}', 'GA\PurchaseOrderController@showPo');
    Route::post('purchase-order/approve', 'GA\PurchaseOrderController@update');
    Route::get('purchase-order/receive/list', 'GA\PurchaseOrderController@receiveList');
    Route::get('purchase-order/receive/{id}/show', 'GA\PurchaseOrderController@receiveShow');
    Route::post('purchase-order/receive/{id}/store', 'GA\PurchaseOrderController@receiveStore');
    Route::get('purchase-order/receive/{id}/penomoran', 'GA\PurchaseOrderController@receiveLabel');
    Route::post('purchase-order/receive/{id}/penomoran', 'GA\PurchaseOrderController@receiveLabelStore');

    //Purchace Order Payment
    Route::resource('purchase-order-payment', 'GA\PurchaseOrderPaymentController');

    //inventory Stock
    Route::resource('inventory-stock', 'GA\InventoryStockController');
    Route::resource('inventory-transfer', 'GA\InventoryTransferController');

    //Asset
    Route::resource('assets', 'Fleet\AssetsController');
    Route::get('assets-2', 'Fleet\AssetsController@index2');
    Route::resource('asset-master-group', 'Fleet\AssetMasterGroupController');
    Route::resource('asset-master-class', 'Fleet\AssetMasterClassController');
    Route::post('assets/pickup', 'Fleet\AssetsController@pickupUpdate');
    Route::resource('asset-destruction', 'GA\AssetDestructionController');
    Route::post('asset-destruction-approve', 'GA\AssetDestructionController@approve');

    Route::resource('stock-opname', 'Fleet\StockOpnameController');

    Route::resource('destruction-income', 'GA\DestructionIncomeController');
    Route::resource('pickup-pr', 'GA\PickupController');
    Route::resource('gps-setting', 'GPS\GpsSettingController');
    Route::resource('gps-map', 'GPS\GpsMapController');
    Route::resource('kategori-kerusakan', 'Fleet\KategoriKerusakanController');
    Route::resource('role-user','Fleet\RoleUserController');
    Route::get('role-user-permission','Fleet\RoleUserController@listPermission');
    Route::resource('location-checklist','Fleet\LocationChecklistController');
    Route::post('location-checklist/check-location','Fleet\LocationChecklistController@checkLocation');
    Route::resource('status-perbaikan','Fleet\StatusPerbaikanController');
    Route::resource('master-vidio','Fleet\MasterVidioController');
    Route::post('master-vidio/history', 'Fleet\MasterVidioController@history');
    Route::post('history-view','Fleet\MasterVidioController@historyView');
    Route::resource('master-location', 'Fleet\MasterLocationController');

    Route::resource('category-asset', 'GA\CategoryAssetController');
    Route::resource('type-asset', 'GA\TypeAssetController');
    Route::post("import-asset", 'Fleet\AssetsController@importAsset');
    Route::resource('accident', 'Fleet\AccidentController');
    Route::post('document-accident/{id}', 'Fleet\AccidentController@addDocument');
    Route::resource('request-form', 'Fleet\RequestFormController');
    Route::post('request-form/ref-no', 'Fleet\RequestFormController@getRefNo');
    Route::post('request-form/reset-detail/{id}', 'Fleet\RequestFormController@resetDetail');
    Route::post('request-form/riwayat-approval/{id}', 'Fleet\RequestFormController@riwayatApproval');
    Route::resource('request-form-detail', 'Fleet\RequestFormDetailController');
    Route::resource('category-item', 'Fleet\CategoryItemController');
    Route::resource('type-item', 'Fleet\TypeItemController');

    Route::get('approval-pairing-list', 'Fleet\TypeItemController@ApprovalPairingList');
    Route::put('pairing-create', 'Fleet\TypeItemController@createPairing');
    Route::get('pairing-edit/{id}', 'Fleet\TypeItemController@editPairing');
    Route::put('pairing-update/{id}', 'Fleet\TypeItemController@updatePairing');
    Route::get('pairing-list', 'Fleet\TypeItemController@pairing');
    Route::get('type-item/pairing/{id}', 'Fleet\TypeItemController@destroyPairing');
    Route::post('type-item-import', 'Fleet\TypeItemController@import');
    Route::post('type-item/approve/{id}', 'Fleet\TypeItemController@approvalRacking');
    Route::post('type-item/reject/{id}', 'Fleet\TypeItemController@rejectRacking');
    Route::resource('handover', 'Fleet\HandOverController');

    Route::get('handover-get-list', 'GA\GoodReceivedController@getSessionItemList');
    Route::post('handover/create-list', 'Fleet\HandOverController@sessionItemList');
    Route::post('received/received-non-asset', 'Fleet\HandOverController@receivedHn');
    Route::get('good-received', 'GA\GoodReceivedController@index');
    Route::post('good-received/upload/{id}', 'GA\GoodReceivedController@uploadDoc');
    Route::get('good-received/detail/{id}', 'GA\GoodReceivedController@detail');
    Route::get('good-received/sort-gr', 'GA\GoodReceivedController@sortGR');
    Route::resource('warehouse', 'Fleet\WarehouseController');
    Route::resource('racking', 'Fleet\RackingController');
    Route::resource('deliver', 'Fleet\DeliverController');
    Route::resource('invoice-send', 'Fleet\InvoiceSendController');
    Route::get('waste-disposal/extermination', 'Fleet\WasteDisposalController@getRequestList');
    Route::resource('waste-disposal', 'Fleet\WasteDisposalController');
    Route::post('waste-disposal/request', 'Fleet\WasteDisposalController@request');
    Route::resource('waste-disposal-detail', 'Fleet\WasteDisposalDetailController');
    Route::resource('request-extermination', 'Fleet\RequestExterminationController');
    Route::resource('location-extermination', 'Fleet\LocationExterminationController');
    Route::resource('term-of-payment', 'Fleet\TermOfPaymentController');
    Route::get('find-organization', 'Fleet\UserController@findUser');
    Route::get('find-type', 'Fleet\CategoryItemController@findType');
    Route::get('find-nama', 'Fleet\MasterTypeAssetController@findNama');
    Route::get('find-approval', 'Fleet\TypeItemController@findApproval');
    Route::get('find-pairing', 'Fleet\TypeItemController@findPairing');
    Route::get('find-data', 'Fleet\TypeItemController@findData');

    Route::get('/no-request-form', [OutboundController::class, 'NoRequestForm']);
    Route::get('/no-referensi', [OutboundController::class, 'NoReferensi']);
    Route::get('/nopol', [OutboundController::class, 'NoPol']);
    Route::get('/area-code', [RackingController::class, 'areaCode']);
    Route::get('/area-code/create', [RackingController::class, 'areaCodeCreate']);
    Route::get('/area-code/{id}/find', [RackingController::class, 'findAreaCode']);

    Route::get('/dashboard/table-assets', [DashboardController::class, 'tableAssets']);
    Route::get('/dashboard/assets', [DashboardController::class, 'Assets']);
    Route::get('/dashboard/detail-assets', [DashboardController::class, 'detailAssets']);
    Route::get('/dashboard/total-kendaraan', [DashboardController::class, 'totalKendaraan']);
    Route::get('/dashboard/persentase-aset', [DashboardController::class, 'persentaseAssets']);
    Route::get('/dashboard/pivot-assets', [DashboardController::class, 'pivotAssets']);

    //Laporan Inbound & Outbound
    Route::get('/dashboard/recap-inbound-outbound', [DashboardController::class, 'recapInboundOutbound']);
    Route::get('/dashboard/pivot-inbound-outbound', [DashboardController::class, 'pivotInboundOutbound']);
    Route::get('/dashboard/inbound-kategori', [DashboardController::class, 'inboundKategori']);
    Route::get('/dashboard/outbound-kategori', [DashboardController::class, 'outboundKategori']);
    Route::get('/dashboard/pending-kategori', [DashboardController::class, 'pendingKategori']);
    Route::get('/dashboard/storage', [DashboardController::class, 'storage']);
    Route::get('/dashboard/total-harga', [DashboardController::class, 'totalHarga']);
    //Laporan Pemusnahan
    Route::get('/dashboard/table-pemusnahan', [DashboardController::class, 'tablePemusnahan']);
    Route::get('/dashboard/total-pemusnahan', [DashboardController::class, 'totalPemusnahan']);
    Route::get('/dashboard/total-ba-pemusnahan', [DashboardController::class, 'totalBAPemusnahan']);
    Route::get('/dashboard/total-amount-pemusnahan', [DashboardController::class, 'totalAmountPemusnahan']);
    Route::get('/dashboard/laporan-pemusnahan', [DashboardController::class, 'laporanPemusnahan']);
    Route::get('/dashboard/pivot-pemusnahan', [DashboardController::class, 'pivotPemusnahan']);
    //Laporan Checklist
    Route::get('/dashboard/table-checklist', [DashboardController::class, 'tableChecklist']);
    Route::get('/dashboard/checklist-kendaraan', [DashboardController::class, 'checklistKendaraan']);
    Route::get('/dashboard/checklist-kesehatan', [DashboardController::class, 'checklistKesehatan']);
    Route::get('/dashboard/checklist-k3', [DashboardController::class, 'checklistK3']);
    Route::get('/dashboard/laporan-checklist', [DashboardController::class, 'laporanChecklist']);
    Route::get('/dashboard/laporan-checklist-company', [DashboardController::class, 'laporanChecklistCompany']);
    Route::get('/dashboard/pivot-checklist', [DashboardController::class, 'pivotChecklist']);
    //Workshop
    Route::get('/dashboard/table-workshop', [DashboardController::class, 'tableWorkshop']);
    Route::get('/dashboard/persentase-kerusakan', [DashboardController::class, 'persentaseKerusakan']);
    Route::get('/dashboard/persentase-service', [DashboardController::class, 'persentaseService']);
    Route::get('/dashboard/persentase-maintenance', [DashboardController::class, 'persentaseMaintenance']);
    Route::get('/dashboard/persentase-perbaikan', [DashboardController::class, 'persentasePerbaikan']);
    Route::get('/dashboard/laporan-workshop', [DashboardController::class, 'laporanWorkshop']);
    Route::get('/dashboard/laporan-workshop-kendaraan', [DashboardController::class, 'laporanWorkshopUsiaKendaraan']);
    Route::get('/dashboard/usia-kendaraan', [DashboardController::class, 'usiaKendaraan']);

    //Berita Acara
    Route::get('/dashboard/aset-vs-nonaset', [DashboardController::class, 'AsetVSNonAset']);
    Route::get('/dashboard/aset-hilang-rusak', [DashboardController::class, 'AsetHilangRusak']);
    Route::get('/dashboard/nonaset-hilang-rusak', [DashboardController::class, 'NonAsetHilangRusak']);
    Route::get('/dashboard/berita-acara-kerusakan', [DashboardController::class, 'beritaAcaraKerusakan']);
    Route::get('/dashboard/amount-ba', [DashboardController::class, 'AmountBA']);
    Route::get('/dashboard/persentase-pembebanan', [DashboardController::class, 'PersentasePembebanan']);
    Route::get('/dashboard/table-ba', [DashboardController::class, 'tableBeritaAcara']);
    Route::get('/dashboard/pivot-ba', [DashboardController::class, 'pivotBA']);
    Route::get('/dashboard/ontimes-ba', [DashboardController::class, 'ontimesBA']);
    Route::get('/dashboard/ontimes-investigasi', [DashboardController::class, 'ontimesInvestigasi']);
    Route::get('/dashboard/ontimes-banding', [DashboardController::class, 'ontimesBanding']);
    Route::get('/dashboard/total-ba', [DashboardController::class, 'totalBA']);
    Route::get('/dashboard/stock-non-aset', [DashboardController::class, 'stockNonAset']);

    Route::get('/dashboard/berita-acara', [DashboardController::class, 'beritaAcara']);
    Route::get('/dashboard/perform-pr', [DashboardController::class, 'performPR']);
    Route::get('/dashboard/perform-gr', [DashboardController::class, 'performGR']);
    Route::get('/dashboard/persentase-pengajuan-retur', [DashboardController::class, 'persentasePengajuanRetur']);
    Route::get('/dashboard/table-pengajuan-retur', [DashboardController::class, 'tablePengajuanRetur']);
    Route::get('/dashboard/persentase-biaya', [DashboardController::class, 'persentaseBiaya']);

    //Stock Opname
    Route::get('/dashboard/LaporanStockOpname', [DashboardController::class, 'LaporanStockOpname']);
    Route::get('/dashboard/detail-opname-assets', [DashboardController::class, 'tableOpname']);

    //retur
    Route::resource('retur', 'Fleet\ReturController');

    Route::group(['prefix' => 'list-asset'], function() {
        Route::get('/', [ListAssetController::class, 'index']);
        Route::get('/asuransi-kendaraan', [ListAssetController::class, 'asuransiKendaraan']);
        Route::post('/asuransi-kendaraan/store', [ListAssetController::class, 'asuransiKendaraanStore']);
        Route::post('/asuransi-kendaraan/import', [ListAssetController::class, 'importAsuransiKendaraan']);
        Route::get('/create', [ListAssetController::class, 'create']);
        Route::post('/upload-media', [ListAssetController::class, 'uploadMedia']);
        Route::post('/import-vehicle',[ListAssetController::class,'importVehicle']);
        Route::post('/import-equipment',[ListAssetController::class,'importEquipment']);
        Route::post('/import-land',[ListAssetController::class,'importLand']);
        Route::post('/import-building',[ListAssetController::class,'importBuilding']);
        Route::post('/import-computer',[ListAssetController::class,'importComputer']);
        Route::post('/import-mechinary',[ListAssetController::class,'importMechinary']);
        Route::post('/import-furniture',[ListAssetController::class,'importFurniture']);
    });

    //mapping-so
    Route::prefix('mapping-so')->group(function() {
        Route::get("/",[MappingSOController::class,'index']);
        Route::post("process-mapping/{id}",[MappingSOController::class,'store']);
        Route::get("edit-mapping/{id}",[MappingSOController::class,'editmapping']);
        Route::post("cek-mapping/{id}",[MappingSOController::class,'cekMapping']);
        Route::post("update-mapping/{id}",[MappingSOController::class,'updateMapping']);
        Route::get('listasset', [MappingSOController::class, 'listasset']);
        Route::get('detailLoc', [MappingSOController::class, 'detailLoc']);
        Route::get('area', [MappingSOController::class, 'area']);
        Route::get('group', [MappingSOController::class, 'group']);
        Route::get('user', [MappingSOController::class, 'getUser']);
    });

    //mapping-so-non-asset
    Route::prefix('mapping-non-asset')->group(function() {
        Route::get("/",[MappingNonController::class,'index']);
        Route::post("process-mapping",[MappingNonController::class,'store']);
        Route::get("edit-mapping/{id}",[MappingNonController::class,'editmapping']);
        Route::post("cek-mapping/{id}",[MappingNonController::class,'cekMapping']);
        Route::post("update-mapping/{id}",[MappingNonController::class,'updateMapping']);
    });

    //so-asset
    Route::prefix('so-asset')->group(function() {
        Route::get("/",[SOAssetController::class,'index']);
        Route::post("ba-so-asset/{id}",[SOAssetController::class,'cekBAPdf']);
        Route::post("la-so-asset/{id}",[SOAssetController::class,'cekLAPdf']);
        Route::post("catatan-ga/{id}",[SOAssetController::class,'catatanGA']);
        Route::post("approved/{id}",[SOAssetController::class,'approved']);
        Route::get('cek-ba/{id}',[SOAssetController::class,'cekBa']);
        Route::get('area',[SOAssetController::class,'area']);
        Route::get('asset',[SOAssetController::class,'index']);
        Route::get('create',[SOAssetController::class,'create']);

        Route::get('cekAsset/{code}',[OpnameController::class,'cekAsset']);
        Route::post('updateAsset',[OpnameController::class,'updateAsset']);
    });

    //ba so asset
    Route::prefix('list-ba-so')->group(function() {
        Route::get("/",[BASOAssetController::class,'index']);
        Route::get("viewBA/{id}",[BASOAssetController::class,'viewBA']);
        Route::post("appr-dept/{id}/{type}/{asset}",[BASOAssetController::class,'apprDept']);
    });

     //ba so asset
     Route::prefix('lap-ba-so')->group(function() {
        Route::get("/",[LapHasilBAAssetController::class,'index']);
        Route::get("viewLA/{id}",[LapHasilBAAssetController::class,'viewLA']);
        Route::post("appr/{id}/{type}/{asset}",[LapHasilBAAssetController::class,'appr']);
    });

    //so-non-asset
    Route::prefix('so-non-asset')->group(function() {
        Route::get("/",[SOAssetController::class,'index2']);
        Route::post("ba-so-asset/{id}",[SOAssetController::class,'cekBAPdf2']);
        Route::post("la-so-asset/{id}",[SOAssetController::class,'cekLAPdf2']);
        Route::post("catatan-ga/{id}",[SOAssetController::class,'catatanGA2']);
        Route::post("approved/{id}",[SOAssetController::class,'approved2']);
    });

    //ba so non asset
    Route::prefix('list-ba-so-non')->group(function() {
        Route::get("/",[BASOAssetController::class,'index2']);
        Route::get("viewBA/{id}",[BASOAssetController::class,'viewBA2']);
        Route::post("appr-dept/{id}/{type}/{asset}",[BASOAssetController::class,'apprDept2']);
    });

    //ba so non asset
        Route::prefix('lap-ba-so-non')->group(function() {
        Route::get("/",[LapHasilBAAssetController::class,'index2']);
        Route::get("viewLA/{id}",[LapHasilBAAssetController::class,'viewLA2']);
        Route::post("appr/{id}/{type}/{asset}",[LapHasilBAAssetController::class,'appr2']);
    });

    Route::group(['prefix'=>'berita-acara'], function() {
        Route::post('/store-ba', [BeritaAcaraController::class, 'store']);
        Route::post('/submit-preview', [BeritaAcaraController::class, 'submitPreview']);
        Route::get('/hasil-investigasi-biaya', [BeritaAcaraController::class, 'listInvestigasiBiaya']);
        Route::get('/hasil-investigasi-biaya/{no_ba}', [BeritaAcaraController::class, 'detailInvestigasiBiaya']);
        Route::post('/hasil-investigasi-biaya/{no_ba}', [BeritaAcaraController::class, 'storeInvestigasiBiaya']);
        Route::get('/list-ba-lengkap', [BeritaAcaraController::class, 'listBaLengkap']);
        Route::post('/upload-file/{id}', [BeritaAcaraController::class, 'uploadFile']);
        Route::get('/list-pengajuan-banding', [BeritaAcaraController::class, 'listPengajuanBanding']);
        Route::get('/pengajuan-banding/{no_ba}', [BeritaAcaraController::class, 'detailPengajuanBanding']);
        Route::post('/pengajuan-banding/{no_ba}', [BeritaAcaraController::class, 'storePengajuanBanding']);
        Route::get('/list-pembebanan-pembiayaan', [BeritaAcaraController::class, 'listPembebanan']);
        Route::get('/get-ba-inbound/{id}', [BeritaAcaraController::class, 'getBAInbound']);
    });

    //laporan
    Route::prefix('report-all')->group(function() {
        Route::get("/",[ReportAllController::class,'index']);
    });

    Route::post('supplier/sync', 'Fleet\SupplierController@syncApiSupplier');
    Route::resource('master-type-asset', 'Fleet\MasterTypeAssetController');

});
// Route::get('set-lokasi-asset', 'Fleet\AssetsController@setLokasi');
